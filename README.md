# 🗜️ 智能解壓縮工具

專為文職人員設計的批量解壓縮工具，能夠自動處理大量ZIP檔案，包含巢狀壓縮檔的遞迴解壓縮。

## ✨ 主要功能

- **批量處理**：一次處理整個資料夾中的所有ZIP檔案
- **遞迴解壓縮**：自動處理壓縮檔中的壓縮檔（巢狀解壓縮）
- **保持結構**：維持原始資料夾結構
- **自動清理**：解壓縮完成後自動刪除原壓縮檔
- **智能衝突處理**：預設自動跳過有檔案衝突的壓縮檔，避免覆蓋現有檔案
- **錯誤處理**：跳過損壞或密碼保護的檔案
- **詳細統計報告**：顯示成功、跳過、錯誤等詳細統計
- **完整日誌記錄**：記錄所有處理過程和結果

## 🚀 使用方法

### 1. 啟動程式
```bash
python smart_extractor.py
```

### 2. 輸入資料夾路徑
- 在檔案總管中複製資料夾路徑
- 貼上到程式中
- 按 Enter 開始處理

### 3. 等待處理完成
程式會自動：
- 搜尋所有ZIP檔案
- 遞迴解壓縮
- 刪除原壓縮檔
- 顯示處理結果

## 📊 詳細處理結果

程式會顯示完整統計：
- ✅ 成功解壓縮的檔案數量
- 🗑️ 成功刪除的檔案數量
- 🔄 覆蓋檔案的數量（如選擇覆蓋模式）
- ⏭️ 跳過解壓縮的檔案數量（預設行為）
- 🔒 密碼保護的檔案數量
- ❌ 處理錯誤的檔案數量
- ⏱️ 總處理時間
- 📋 跳過檔案的詳細原因

## 📝 日誌記錄

所有處理過程都會記錄在 `extraction_log.txt` 檔案中，包括：
- 處理時間
- 檔案路徑
- 成功/失敗狀態
- 錯誤訊息

## ⚠️ 注意事項

1. **檔案衝突處理**：預設會自動跳過有檔案衝突的壓縮檔，保護現有檔案不被覆蓋
2. **備份重要資料**：程式會刪除原壓縮檔，請確保重要資料有備份
3. **密碼保護**：密碼保護的ZIP檔案會被跳過
4. **檔案權限**：確保有足夠權限讀寫目標資料夾
5. **磁碟空間**：確保有足夠空間存放解壓縮後的檔案

## 🛠️ 系統需求

- Python 3.7+
- Windows/macOS/Linux
- 足夠的磁碟空間

## 📞 使用範例

```
🗜️  智能解壓縮工具
========================================

請輸入要處理的資料夾路徑（或輸入 'q' 退出）:
資料夾路徑: C:\工作文件\待處理資料

開始批量解壓縮處理: C:\工作文件\待處理資料
找到 25 個ZIP檔案
正在解壓縮: C:\工作文件\待處理資料\檔案1.zip
成功解壓縮: C:\工作文件\待處理資料\檔案1.zip
已刪除: C:\工作文件\待處理資料\檔案1.zip
...

==================================================
處理完成！統計結果：
成功解壓縮: 25 個檔案
成功刪除: 25 個檔案
處理錯誤: 0 個檔案
總處理時間: 12.34 秒
==================================================
```
