#  SelfTest/Protocol/__init__.py: Self-tests for Cryptodome.Protocol
#
# Written in 2008 by <PERSON><PERSON> <<EMAIL>>
#
# ===================================================================
# The contents of this file are dedicated to the public domain.  To
# the extent that dedication to the public domain is not available,
# everyone is granted a worldwide, perpetual, royalty-free,
# non-exclusive license to exercise all rights associated with the
# contents of this file for any purpose whatsoever.
# No rights are reserved.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
# BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
# ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.
# ===================================================================

import sys

"""Self-test for Cryptodome.Protocol"""

def get_tests(config={}):
    tests = []
    from Cryptodome.SelfTest.Protocol import test_rfc1751;  tests += test_rfc1751.get_tests(config=config)
    from Cryptodome.SelfTest.Protocol import test_KDF;      tests += test_KDF.get_tests(config=config)
    from Cryptodome.SelfTest.Protocol import test_ecdh;     tests += test_ecdh.get_tests(config=config)

    from Cryptodome.SelfTest.Protocol import test_SecretSharing
    tests += test_SecretSharing.get_tests(config=config)

    if sys.version_info >= (3, 9):
        from Cryptodome.SelfTest.Protocol import test_HPKE
        tests += test_HPKE.get_tests(config=config)

    return tests

if __name__ == '__main__':
    import unittest
    suite = lambda: unittest.TestSuite(get_tests())
    unittest.main(defaultTest='suite')
