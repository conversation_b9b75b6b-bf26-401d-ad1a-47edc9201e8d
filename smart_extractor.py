#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能解壓縮工具
支援批量處理、遞迴解壓縮巢狀壓縮檔、自動刪除原檔案
"""

import os
import zipfile
import shutil
import logging
from pathlib import Path
from typing import List, Set
import time

class SmartExtractor:
    def __init__(self, log_level=logging.INFO, conflict_strategy='ask'):
        """
        初始化智能解壓縮器
        conflict_strategy: 'ask', 'overwrite', 'skip', 'rename'
        """
        self.setup_logging(log_level)
        self.processed_files: Set[str] = set()
        self.extracted_count = 0
        self.deleted_count = 0
        self.error_count = 0
        self.skipped_count = 0
        self.overwritten_count = 0
        self.renamed_count = 0
        self.password_protected_count = 0
        self.conflict_strategy = conflict_strategy
        self.conflict_details = []
        
    def setup_logging(self, level):
        """設置日誌記錄"""
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('extraction_log.txt', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def is_zip_file(self, file_path: str) -> bool:
        """檢查是否為有效的ZIP檔案"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                # 嘗試讀取檔案列表來驗證ZIP檔案完整性
                zip_ref.namelist()
                return True
        except (zipfile.BadZipFile, FileNotFoundError, PermissionError):
            return False
    
    def check_file_conflicts(self, zip_path: str, extract_to: str) -> tuple:
        """檢查解壓縮時的檔案衝突"""
        conflicts = []
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    if not file_info.is_dir():
                        target_path = os.path.join(extract_to, file_info.filename)
                        if os.path.exists(target_path):
                            conflicts.append({
                                'zip_file': file_info.filename,
                                'target_path': target_path,
                                'existing_size': os.path.getsize(target_path),
                                'new_size': file_info.file_size
                            })
        except Exception as e:
            self.logger.error(f"檢查衝突時發生錯誤 {zip_path}: {str(e)}")

        return conflicts, len(conflicts) > 0

    def handle_conflict_strategy(self, zip_path: str, conflicts: list) -> str:
        """處理檔案衝突策略"""
        if not conflicts:
            return 'proceed'

        if self.conflict_strategy == 'overwrite':
            self.overwritten_count += len(conflicts)
            return 'proceed'
        elif self.conflict_strategy == 'skip':
            self.skipped_count += 1
            self.conflict_details.append({
                'zip_file': zip_path,
                'reason': 'file_conflict',
                'conflicts': len(conflicts)
            })
            return 'skip'
        elif self.conflict_strategy == 'ask':
            print(f"\n⚠️  檔案衝突警告:")
            print(f"ZIP檔案: {os.path.basename(zip_path)}")
            print(f"發現 {len(conflicts)} 個檔案衝突:")
            for i, conflict in enumerate(conflicts[:3]):  # 只顯示前3個
                print(f"  {i+1}. {conflict['zip_file']}")
            if len(conflicts) > 3:
                print(f"  ... 還有 {len(conflicts)-3} 個衝突")

            while True:
                choice = input("選擇處理方式 [O]覆蓋 / [S]跳過 / [A]全部覆蓋 / [K]全部跳過: ").upper()
                if choice == 'O':
                    self.overwritten_count += len(conflicts)
                    return 'proceed'
                elif choice == 'S':
                    self.skipped_count += 1
                    self.conflict_details.append({
                        'zip_file': zip_path,
                        'reason': 'user_skip',
                        'conflicts': len(conflicts)
                    })
                    return 'skip'
                elif choice == 'A':
                    self.conflict_strategy = 'overwrite'
                    self.overwritten_count += len(conflicts)
                    return 'proceed'
                elif choice == 'K':
                    self.conflict_strategy = 'skip'
                    self.skipped_count += 1
                    self.conflict_details.append({
                        'zip_file': zip_path,
                        'reason': 'user_skip_all',
                        'conflicts': len(conflicts)
                    })
                    return 'skip'
                else:
                    print("請輸入有效選項: O, S, A, 或 K")

        return 'skip'

    def extract_zip(self, zip_path: str, extract_to: str) -> bool:
        """解壓縮單個ZIP檔案"""
        try:
            self.logger.info(f"正在檢查: {zip_path}")

            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # 檢查是否有密碼保護
                try:
                    zip_ref.testzip()
                except RuntimeError as e:
                    if "Bad password" in str(e) or "password required" in str(e).lower():
                        self.logger.warning(f"跳過密碼保護的檔案: {zip_path}")
                        self.password_protected_count += 1
                        self.conflict_details.append({
                            'zip_file': zip_path,
                            'reason': 'password_protected',
                            'conflicts': 0
                        })
                        return False

            # 檢查檔案衝突
            conflicts, has_conflicts = self.check_file_conflicts(zip_path, extract_to)

            # 處理衝突策略
            action = self.handle_conflict_strategy(zip_path, conflicts)

            if action == 'skip':
                self.logger.info(f"跳過解壓縮: {zip_path}")
                return False

            # 執行解壓縮
            self.logger.info(f"正在解壓縮: {zip_path}")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
                self.extracted_count += 1
                self.logger.info(f"成功解壓縮: {zip_path}")
                return True

        except Exception as e:
            self.logger.error(f"解壓縮失敗 {zip_path}: {str(e)}")
            self.error_count += 1
            return False
    
    def find_zip_files(self, directory: str) -> List[str]:
        """遞迴搜尋目錄中的所有ZIP檔案"""
        zip_files = []
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.lower().endswith('.zip'):
                        full_path = os.path.join(root, file)
                        if self.is_zip_file(full_path):
                            zip_files.append(full_path)
                        else:
                            self.logger.warning(f"無效的ZIP檔案: {full_path}")
        except Exception as e:
            self.logger.error(f"搜尋ZIP檔案時發生錯誤: {str(e)}")
        
        return zip_files
    
    def safe_delete_file(self, file_path: str) -> bool:
        """安全刪除檔案"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                self.deleted_count += 1
                self.logger.info(f"已刪除: {file_path}")
                return True
        except Exception as e:
            self.logger.error(f"刪除檔案失敗 {file_path}: {str(e)}")
        return False
    
    def process_directory_recursive(self, directory: str, max_depth: int = 10) -> None:
        """遞迴處理目錄中的所有ZIP檔案（包含巢狀解壓縮）"""
        if max_depth <= 0:
            self.logger.warning(f"達到最大遞迴深度，停止處理: {directory}")
            return
        
        self.logger.info(f"開始處理目錄: {directory} (剩餘深度: {max_depth})")
        
        # 找到當前目錄中的所有ZIP檔案
        zip_files = self.find_zip_files(directory)
        
        if not zip_files:
            self.logger.info(f"目錄中沒有找到ZIP檔案: {directory}")
            return
        
        self.logger.info(f"找到 {len(zip_files)} 個ZIP檔案")
        
        # 處理每個ZIP檔案
        for zip_path in zip_files:
            if zip_path in self.processed_files:
                continue
                
            self.processed_files.add(zip_path)
            
            # 獲取解壓縮目標目錄（ZIP檔案所在的目錄）
            extract_to = os.path.dirname(zip_path)
            
            # 解壓縮
            if self.extract_zip(zip_path, extract_to):
                # 解壓縮成功後刪除原檔案
                self.safe_delete_file(zip_path)
                
                # 遞迴處理新解壓縮的內容（可能包含更多ZIP檔案）
                self.process_directory_recursive(extract_to, max_depth - 1)
    
    def print_detailed_report(self, duration: float) -> None:
        """印出詳細的處理報告"""
        print("\n" + "=" * 60)
        print("🎉 處理完成！詳細統計結果：")
        print("=" * 60)

        print(f"✅ 成功解壓縮: {self.extracted_count} 個檔案")
        print(f"🗑️  成功刪除: {self.deleted_count} 個檔案")

        if self.overwritten_count > 0:
            print(f"🔄 覆蓋檔案: {self.overwritten_count} 個檔案")

        if self.skipped_count > 0:
            print(f"⏭️  跳過解壓縮: {self.skipped_count} 個檔案")

        if self.password_protected_count > 0:
            print(f"🔒 密碼保護: {self.password_protected_count} 個檔案")

        if self.error_count > 0:
            print(f"❌ 處理錯誤: {self.error_count} 個檔案")

        print(f"⏱️  總處理時間: {duration:.2f} 秒")

        # 顯示跳過檔案的詳細資訊
        if self.conflict_details:
            print("\n📋 跳過檔案詳細資訊:")
            print("-" * 40)
            for detail in self.conflict_details:
                zip_name = os.path.basename(detail['zip_file'])
                reason_map = {
                    'file_conflict': '檔案衝突',
                    'user_skip': '使用者選擇跳過',
                    'user_skip_all': '使用者選擇全部跳過',
                    'password_protected': '密碼保護'
                }
                reason = reason_map.get(detail['reason'], detail['reason'])
                if detail['conflicts'] > 0:
                    print(f"  📁 {zip_name} - {reason} ({detail['conflicts']} 個衝突)")
                else:
                    print(f"  📁 {zip_name} - {reason}")

        print("=" * 60)

    def process_folder(self, folder_path: str, conflict_strategy: str = 'ask') -> None:
        """處理指定資料夾的主要入口函數"""
        if not os.path.exists(folder_path):
            self.logger.error(f"資料夾不存在: {folder_path}")
            return

        if not os.path.isdir(folder_path):
            self.logger.error(f"路徑不是資料夾: {folder_path}")
            return

        self.logger.info(f"開始批量解壓縮處理: {folder_path}")
        start_time = time.time()

        # 重置計數器和設定
        self.extracted_count = 0
        self.deleted_count = 0
        self.error_count = 0
        self.skipped_count = 0
        self.overwritten_count = 0
        self.renamed_count = 0
        self.password_protected_count = 0
        self.processed_files.clear()
        self.conflict_details.clear()
        self.conflict_strategy = conflict_strategy

        # 開始遞迴處理
        self.process_directory_recursive(folder_path)

        # 處理完成統計
        end_time = time.time()
        duration = end_time - start_time

        # 顯示詳細報告
        self.print_detailed_report(duration)

        # 記錄到日誌
        self.logger.info("=" * 50)
        self.logger.info("處理完成！統計結果：")
        self.logger.info(f"成功解壓縮: {self.extracted_count} 個檔案")
        self.logger.info(f"成功刪除: {self.deleted_count} 個檔案")
        self.logger.info(f"覆蓋檔案: {self.overwritten_count} 個檔案")
        self.logger.info(f"跳過檔案: {self.skipped_count} 個檔案")
        self.logger.info(f"密碼保護: {self.password_protected_count} 個檔案")
        self.logger.info(f"處理錯誤: {self.error_count} 個檔案")
        self.logger.info(f"總處理時間: {duration:.2f} 秒")
        self.logger.info("=" * 50)


def main():
    """主程式入口"""
    print("🗜️  智能解壓縮工具 v2.0")
    print("=" * 50)
    print("新功能：檔案衝突處理、詳細統計報告")
    print("=" * 50)

    extractor = SmartExtractor()

    # 設定衝突處理策略
    print("\n⚙️  衝突處理設定:")
    print("當解壓縮檔案與現有檔案衝突時：")
    print("1. 每次詢問 (預設)")
    print("2. 自動覆蓋所有衝突檔案")
    print("3. 自動跳過所有衝突檔案")

    while True:
        strategy_choice = input("\n選擇處理策略 [1-3] (直接按Enter使用預設): ").strip()
        if strategy_choice == '' or strategy_choice == '1':
            conflict_strategy = 'ask'
            print("✅ 設定為：每次詢問")
            break
        elif strategy_choice == '2':
            conflict_strategy = 'overwrite'
            print("✅ 設定為：自動覆蓋")
            break
        elif strategy_choice == '3':
            conflict_strategy = 'skip'
            print("✅ 設定為：自動跳過")
            break
        else:
            print("❌ 請輸入 1、2 或 3")

    while True:
        print("\n" + "=" * 50)
        print("請輸入要處理的資料夾路徑（或輸入 'q' 退出）:")
        folder_path = input("📁 資料夾路徑: ").strip()

        if folder_path.lower() == 'q':
            print("👋 再見！")
            break

        if not folder_path:
            print("❌ 請輸入有效的資料夾路徑")
            continue

        # 處理路徑中的引號
        folder_path = folder_path.strip('"\'')

        try:
            extractor.process_folder(folder_path, conflict_strategy)
        except KeyboardInterrupt:
            print("\n⚠️  用戶中斷操作")
            break
        except Exception as e:
            print(f"❌ 發生未預期的錯誤: {str(e)}")

        # 詢問是否繼續
        continue_choice = input("\n是否繼續處理其他資料夾？ [Y/n]: ").strip().lower()
        if continue_choice == 'n':
            print("👋 再見！")
            break


if __name__ == "__main__":
    main()
