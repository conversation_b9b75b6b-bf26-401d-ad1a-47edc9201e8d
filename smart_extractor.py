#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能解壓縮工具
支援批量處理、遞迴解壓縮巢狀壓縮檔、自動刪除原檔案
"""

import os
import zipfile
import shutil
import logging
from pathlib import Path
from typing import List, Set
import time

class SmartExtractor:
    def __init__(self, log_level=logging.INFO):
        """初始化智能解壓縮器"""
        self.setup_logging(log_level)
        self.processed_files: Set[str] = set()
        self.extracted_count = 0
        self.deleted_count = 0
        self.error_count = 0
        
    def setup_logging(self, level):
        """設置日誌記錄"""
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('extraction_log.txt', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def is_zip_file(self, file_path: str) -> bool:
        """檢查是否為有效的ZIP檔案"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                # 嘗試讀取檔案列表來驗證ZIP檔案完整性
                zip_ref.namelist()
                return True
        except (zipfile.BadZipFile, FileNotFoundError, PermissionError):
            return False
    
    def extract_zip(self, zip_path: str, extract_to: str) -> bool:
        """解壓縮單個ZIP檔案"""
        try:
            self.logger.info(f"正在解壓縮: {zip_path}")
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # 檢查是否有密碼保護
                try:
                    zip_ref.testzip()
                except RuntimeError as e:
                    if "Bad password" in str(e) or "password required" in str(e).lower():
                        self.logger.warning(f"跳過密碼保護的檔案: {zip_path}")
                        return False
                
                # 解壓縮所有檔案
                zip_ref.extractall(extract_to)
                self.extracted_count += 1
                self.logger.info(f"成功解壓縮: {zip_path}")
                return True
                
        except Exception as e:
            self.logger.error(f"解壓縮失敗 {zip_path}: {str(e)}")
            self.error_count += 1
            return False
    
    def find_zip_files(self, directory: str) -> List[str]:
        """遞迴搜尋目錄中的所有ZIP檔案"""
        zip_files = []
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.lower().endswith('.zip'):
                        full_path = os.path.join(root, file)
                        if self.is_zip_file(full_path):
                            zip_files.append(full_path)
                        else:
                            self.logger.warning(f"無效的ZIP檔案: {full_path}")
        except Exception as e:
            self.logger.error(f"搜尋ZIP檔案時發生錯誤: {str(e)}")
        
        return zip_files
    
    def safe_delete_file(self, file_path: str) -> bool:
        """安全刪除檔案"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                self.deleted_count += 1
                self.logger.info(f"已刪除: {file_path}")
                return True
        except Exception as e:
            self.logger.error(f"刪除檔案失敗 {file_path}: {str(e)}")
        return False
    
    def process_directory_recursive(self, directory: str, max_depth: int = 10) -> None:
        """遞迴處理目錄中的所有ZIP檔案（包含巢狀解壓縮）"""
        if max_depth <= 0:
            self.logger.warning(f"達到最大遞迴深度，停止處理: {directory}")
            return
        
        self.logger.info(f"開始處理目錄: {directory} (剩餘深度: {max_depth})")
        
        # 找到當前目錄中的所有ZIP檔案
        zip_files = self.find_zip_files(directory)
        
        if not zip_files:
            self.logger.info(f"目錄中沒有找到ZIP檔案: {directory}")
            return
        
        self.logger.info(f"找到 {len(zip_files)} 個ZIP檔案")
        
        # 處理每個ZIP檔案
        for zip_path in zip_files:
            if zip_path in self.processed_files:
                continue
                
            self.processed_files.add(zip_path)
            
            # 獲取解壓縮目標目錄（ZIP檔案所在的目錄）
            extract_to = os.path.dirname(zip_path)
            
            # 解壓縮
            if self.extract_zip(zip_path, extract_to):
                # 解壓縮成功後刪除原檔案
                self.safe_delete_file(zip_path)
                
                # 遞迴處理新解壓縮的內容（可能包含更多ZIP檔案）
                self.process_directory_recursive(extract_to, max_depth - 1)
    
    def process_folder(self, folder_path: str) -> None:
        """處理指定資料夾的主要入口函數"""
        if not os.path.exists(folder_path):
            self.logger.error(f"資料夾不存在: {folder_path}")
            return
        
        if not os.path.isdir(folder_path):
            self.logger.error(f"路徑不是資料夾: {folder_path}")
            return
        
        self.logger.info(f"開始批量解壓縮處理: {folder_path}")
        start_time = time.time()
        
        # 重置計數器
        self.extracted_count = 0
        self.deleted_count = 0
        self.error_count = 0
        self.processed_files.clear()
        
        # 開始遞迴處理
        self.process_directory_recursive(folder_path)
        
        # 處理完成統計
        end_time = time.time()
        duration = end_time - start_time
        
        self.logger.info("=" * 50)
        self.logger.info("處理完成！統計結果：")
        self.logger.info(f"成功解壓縮: {self.extracted_count} 個檔案")
        self.logger.info(f"成功刪除: {self.deleted_count} 個檔案")
        self.logger.info(f"處理錯誤: {self.error_count} 個檔案")
        self.logger.info(f"總處理時間: {duration:.2f} 秒")
        self.logger.info("=" * 50)


def main():
    """主程式入口"""
    print("🗜️  智能解壓縮工具")
    print("=" * 40)
    
    extractor = SmartExtractor()
    
    while True:
        print("\n請輸入要處理的資料夾路徑（或輸入 'q' 退出）:")
        folder_path = input("資料夾路徑: ").strip()
        
        if folder_path.lower() == 'q':
            print("再見！")
            break
        
        if not folder_path:
            print("請輸入有效的資料夾路徑")
            continue
        
        # 處理路徑中的引號
        folder_path = folder_path.strip('"\'')
        
        try:
            extractor.process_folder(folder_path)
        except KeyboardInterrupt:
            print("\n用戶中斷操作")
            break
        except Exception as e:
            print(f"發生未預期的錯誤: {str(e)}")


if __name__ == "__main__":
    main()
